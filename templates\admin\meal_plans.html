{% extends "base.html" %}

{% block title %}Quản lý kế hoạch bữa ăn - OpenFood Admin{% endblock %}

{% block page_title %}Quản lý kế hoạch bữa ăn{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="refreshMealPlans()">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
    <button type="button" class="btn btn-outline-success" onclick="exportMealPlans()">
        <i class="fas fa-download"></i> Xuất danh sách
    </button>
</div>
{% endblock %}

{% block content %}
<!-- Filter -->
<div class="row mb-4">
    <div class="col-md-8">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="user_id" placeholder="Lọc theo User ID..." value="{{ user_id }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-filter"></i> Lọc
            </button>
            {% if user_id %}
            <a href="/admin/meal-plans" class="btn btn-outline-secondary ms-2">
                <i class="fas fa-times"></i> Xóa bộ lọc
            </a>
            {% endif %}
        </form>
    </div>
    <div class="col-md-4 text-end">
        <div class="text-muted">
            Tổng cộng: <strong>{{ total_plans }}</strong> kế hoạch
        </div>
    </div>
</div>

{% if error %}
<div class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle"></i> {{ error }}
</div>
{% endif %}

<!-- Meal Plans Table -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách kế hoạch bữa ăn</h6>
    </div>
    <div class="card-body">
        {% if meal_plans %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>ID</th>
                        <th>Người dùng</th>
                        <th>Thời gian</th>
                        <th>Mục tiêu dinh dưỡng</th>
                        <th>Số ngày</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    {% for plan in meal_plans %}
                    <tr>
                        <td>
                            <code>{{ plan.id[:8] }}...</code>
                        </td>
                        <td>
                            <div>
                                <strong>{{ plan.user_id[:8] }}...</strong>
                            </div>
                            <small class="text-muted">{{ plan.user_email or 'Không rõ' }}</small>
                        </td>
                        <td>
                            <div>
                                <strong>{{ plan.start_date or 'Không rõ' }}</strong>
                            </div>
                            <small class="text-muted">đến {{ plan.end_date or 'Không rõ' }}</small>
                        </td>
                        <td>
                            {% if plan.nutrition_goals %}
                            <div class="nutrition-summary">
                                <small>
                                    <i class="fas fa-fire text-danger"></i> {{ plan.nutrition_goals.calories or 0 }} kcal<br>
                                    <i class="fas fa-drumstick-bite text-warning"></i> {{ plan.nutrition_goals.protein or 0 }}g protein<br>
                                    <i class="fas fa-bread-slice text-info"></i> {{ plan.nutrition_goals.carbs or 0 }}g carbs
                                </small>
                            </div>
                            {% else %}
                            <small class="text-muted">Chưa có mục tiêu</small>
                            {% endif %}
                        </td>
                        <td>
                            <span class="badge bg-info">{{ plan.days|length if plan.days else 0 }} ngày</span>
                        </td>
                        <td>
                            {% if plan.is_active %}
                            <span class="badge bg-success">Đang sử dụng</span>
                            {% else %}
                            <span class="badge bg-secondary">Không hoạt động</span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="viewMealPlan('{{ plan.id }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="editMealPlan('{{ plan.id }}')">
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button type="button" class="btn btn-outline-danger" onclick="deleteMealPlan('{{ plan.id }}')">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <nav aria-label="Meal plan pagination">
            <ul class="pagination justify-content-center">
                {% if has_prev %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page - 1 }}{% if user_id %}&user_id={{ user_id }}{% endif %}">Trước</a>
                </li>
                {% endif %}
                
                {% for page_num in range(1, total_pages + 1) %}
                {% if page_num == current_page %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_num }}{% if user_id %}&user_id={{ user_id }}{% endif %}">{{ page_num }}</a>
                </li>
                {% elif page_num == 4 or page_num == total_pages - 3 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page + 1 }}{% if user_id %}&user_id={{ user_id }}{% endif %}">Sau</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không có kế hoạch bữa ăn nào</h5>
            <p class="text-muted">
                {% if user_id %}
                Không tìm thấy kế hoạch nào cho User ID "{{ user_id }}"
                {% else %}
                Hệ thống chưa có kế hoạch bữa ăn nào
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>

<!-- Meal Plan Detail Modal -->
<div class="modal fade" id="mealPlanDetailModal" tabindex="-1" aria-labelledby="mealPlanDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="mealPlanDetailModalLabel">Chi tiết kế hoạch bữa ăn</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="mealPlanDetailContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.nutrition-summary {
    font-size: 0.85rem;
}
.meal-day-card {
    border-left: 4px solid #007bff;
}
.meal-item {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 8px;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
function refreshMealPlans() {
    location.reload();
}

function exportMealPlans() {
    // Implement export functionality
    alert('Tính năng xuất danh sách kế hoạch bữa ăn sẽ được triển khai sau');
}

function viewMealPlan(planId) {
    // Show meal plan detail modal
    const modal = new bootstrap.Modal(document.getElementById('mealPlanDetailModal'));
    const content = document.getElementById('mealPlanDetailContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Load meal plan details (implement API call)
    setTimeout(() => {
        content.innerHTML = `
            <div class="row mb-4">
                <div class="col-md-6">
                    <h6>Thông tin kế hoạch</h6>
                    <p><strong>ID:</strong> ${planId}</p>
                    <p><strong>Thời gian:</strong> 01/01/2024 - 07/01/2024</p>
                    <p><strong>Trạng thái:</strong> <span class="badge bg-success">Đang sử dụng</span></p>
                </div>
                <div class="col-md-6">
                    <h6>Mục tiêu dinh dưỡng</h6>
                    <p><strong>Calories:</strong> 2000 kcal/ngày</p>
                    <p><strong>Protein:</strong> 150g/ngày</p>
                    <p><strong>Carbs:</strong> 250g/ngày</p>
                </div>
            </div>
            <hr>
            <h6>Chi tiết các ngày</h6>
            <div class="row">
                <div class="col-md-4">
                    <div class="card meal-day-card">
                        <div class="card-header">
                            <strong>Thứ 2</strong>
                        </div>
                        <div class="card-body">
                            <div class="meal-item">
                                <strong>Bữa sáng:</strong> Phở bò
                            </div>
                            <div class="meal-item">
                                <strong>Bữa trưa:</strong> Cơm gà
                            </div>
                            <div class="meal-item">
                                <strong>Bữa tối:</strong> Bún bò Huế
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Add more days as needed -->
            </div>
        `;
    }, 1000);
}

function editMealPlan(planId) {
    // Implement edit functionality
    alert('Tính năng chỉnh sửa kế hoạch bữa ăn sẽ được triển khai sau');
}

function deleteMealPlan(planId) {
    if (confirm('Bạn có chắc chắn muốn xóa kế hoạch bữa ăn này?')) {
        // Implement delete API call
        alert('Tính năng xóa kế hoạch bữa ăn sẽ được triển khai sau');
    }
}
</script>
{% endblock %}

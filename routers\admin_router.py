from fastapi import APIRouter, Request, Depends, HTTPException, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from typing import List, Dict, Optional, Any
import os
from datetime import datetime, timedelta
import json

# Import services
from services.firestore_service import firestore_service
from auth_utils import get_current_user, TokenPayload

router = APIRouter(prefix="/admin", tags=["Admin"])

# Template instance
def get_templates():
    base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    return Jinja2Templates(directory=os.path.join(base_dir, "templates"))

# Import food_items from openfood_router
from routers.openfood_router import food_items

def get_system_stats():
    """Lấy thống kê tổng quan của hệ thống"""
    try:
        # Thống kê món ăn
        total_foods = len(food_items)
        
        # Thống kê người dùng (từ Firestore)
        try:
            users = firestore_service.get_all_users()
            active_users = len([u for u in users if u.get('last_login')])
        except:
            active_users = 0
        
        # Thống kê meal plans
        try:
            meal_plans = firestore_service.get_all_meal_plans()
            total_meal_plans = len(meal_plans)
        except:
            total_meal_plans = 0
        
        # API calls hôm nay (giả lập)
        api_calls_today = 150  # Có thể implement tracking thực tế
        
        return {
            "total_foods": total_foods,
            "active_users": active_users,
            "total_meal_plans": total_meal_plans,
            "api_calls_today": api_calls_today
        }
    except Exception as e:
        print(f"Error getting system stats: {str(e)}")
        return {
            "total_foods": 0,
            "active_users": 0,
            "total_meal_plans": 0,
            "api_calls_today": 0
        }

def get_recent_activities():
    """Lấy hoạt động gần đây từ Firebase"""
    try:
        activities = []

        # Lấy meal plans gần đây
        recent_meal_plans = firestore_service.get_all_meal_plans()
        for plan in recent_meal_plans[:3]:  # Lấy 3 meal plans gần nhất
            activities.append({
                "action": "Tạo meal plan",
                "description": f"Kế hoạch bữa ăn cho user {plan.get('user_id', 'Unknown')[:8]}...",
                "timestamp": plan.get('created_at', 'Không rõ')
            })

        # Lấy người dùng mới
        recent_users = firestore_service.get_all_users()
        for user in recent_users[:2]:  # Lấy 2 users gần nhất
            activities.append({
                "action": "Người dùng đăng ký",
                "description": f"Người dùng mới: {user.get('email', 'Unknown')}",
                "timestamp": user.get('created_at', 'Không rõ')
            })

        # Sắp xếp theo thời gian (nếu có)
        return activities[:5]  # Trả về 5 hoạt động gần nhất

    except Exception as e:
        print(f"Error getting recent activities: {str(e)}")
        # Fallback to mock data
        return [
            {
                "action": "Hệ thống khởi động",
                "description": "Server đã khởi động thành công",
                "timestamp": "Vừa xong"
            }
        ]

def get_recent_foods():
    """Lấy món ăn được tạo gần đây từ food_items"""
    try:
        # Sắp xếp theo thời gian tạo và lấy 5 món gần nhất
        sorted_foods = sorted(food_items, key=lambda x: x.get('created_at', ''), reverse=True)

        # Format lại dữ liệu để hiển thị đẹp hơn
        formatted_foods = []
        for food in sorted_foods[:5]:
            formatted_foods.append({
                "id": food.get('id', ''),
                "name": food.get('name', 'Không rõ'),
                "nutrition": {
                    "calories": food.get('nutrition', {}).get('calories', 0)
                },
                "created_at": food.get('created_at', 'Không rõ')
            })

        return formatted_foods
    except Exception as e:
        print(f"Error getting recent foods: {str(e)}")
        return []

def get_chart_data():
    """Tạo dữ liệu cho biểu đồ"""
    # Dữ liệu hoạt động 7 ngày qua (giả lập)
    activity_labels = []
    activity_data = []
    
    for i in range(7):
        date = datetime.now() - timedelta(days=6-i)
        activity_labels.append(date.strftime("%d/%m"))
        activity_data.append(50 + i * 10 + (i % 3) * 20)  # Giả lập dữ liệu
    
    # Dữ liệu loại món ăn
    food_type_labels = ["Bữa sáng", "Bữa trưa", "Bữa tối", "Đồ uống", "Tráng miệng"]
    food_type_data = [25, 35, 30, 5, 5]  # Giả lập phần trăm
    
    return {
        "activity_labels": activity_labels,
        "activity_data": activity_data,
        "food_type_labels": food_type_labels,
        "food_type_data": food_type_data
    }

def get_system_status():
    """Kiểm tra trạng thái hệ thống"""
    try:
        # Kiểm tra AI service
        from groq_integration import groq_service
        ai_available = groq_service.available
        ai_type = "LLaMA 3 (Groq)" if ai_available else None
    except:
        ai_available = False
        ai_type = None
    
    # Kiểm tra Firebase
    try:
        firebase_connected = firestore_service.check_connection()
    except:
        firebase_connected = False
    
    return {
        "ai_available": ai_available,
        "ai_type": ai_type,
        "firebase_connected": firebase_connected
    }

@router.get("/", response_class=HTMLResponse)
@router.get("/dashboard", response_class=HTMLResponse)
async def admin_dashboard(
    request: Request, 
    templates: Jinja2Templates = Depends(get_templates)
):
    """Trang dashboard admin"""
    try:
        # Lấy dữ liệu thống kê
        stats = get_system_stats()
        recent_activities = get_recent_activities()
        recent_foods = get_recent_foods()
        chart_data = get_chart_data()
        system_status = get_system_status()
        
        return templates.TemplateResponse("admin/dashboard.html", {
            "request": request,
            "stats": stats,
            "recent_activities": recent_activities,
            "recent_foods": recent_foods,
            "activity_chart_labels": chart_data["activity_labels"],
            "activity_chart_data": chart_data["activity_data"],
            "food_type_labels": chart_data["food_type_labels"],
            "food_type_data": chart_data["food_type_data"],
            "system_status": system_status
        })
    except Exception as e:
        print(f"Error in admin dashboard: {str(e)}")
        # Trả về trang với dữ liệu mặc định
        return templates.TemplateResponse("admin/dashboard.html", {
            "request": request,
            "stats": {"total_foods": 0, "active_users": 0, "total_meal_plans": 0, "api_calls_today": 0},
            "recent_activities": [],
            "recent_foods": [],
            "activity_chart_labels": [],
            "activity_chart_data": [],
            "food_type_labels": [],
            "food_type_data": [],
            "system_status": {"ai_available": False, "ai_type": None, "firebase_connected": False}
        })

@router.get("/users", response_class=HTMLResponse)
async def admin_users(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    search: Optional[str] = None,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Trang quản lý người dùng"""
    try:
        # Lấy danh sách người dùng từ Firestore
        users = firestore_service.get_all_users()
        
        # Lọc theo từ khóa tìm kiếm
        if search:
            search = search.lower()
            users = [
                user for user in users 
                if search in user.get('email', '').lower() or 
                   search in user.get('display_name', '').lower()
            ]
        
        # Phân trang
        total_users = len(users)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        users_page = users[start_idx:end_idx]
        
        # Tính toán thông tin phân trang
        total_pages = (total_users + limit - 1) // limit
        has_prev = page > 1
        has_next = page < total_pages
        
        return templates.TemplateResponse("admin/users.html", {
            "request": request,
            "users": users_page,
            "current_page": page,
            "total_pages": total_pages,
            "total_users": total_users,
            "has_prev": has_prev,
            "has_next": has_next,
            "search": search or ""
        })
    except Exception as e:
        print(f"Error in admin users: {str(e)}")
        return templates.TemplateResponse("admin/users.html", {
            "request": request,
            "users": [],
            "current_page": 1,
            "total_pages": 1,
            "total_users": 0,
            "has_prev": False,
            "has_next": False,
            "search": search or "",
            "error": f"Lỗi khi tải dữ liệu người dùng: {str(e)}"
        })

@router.get("/meal-plans", response_class=HTMLResponse)
async def admin_meal_plans(
    request: Request,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    user_id: Optional[str] = None,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Trang quản lý kế hoạch bữa ăn"""
    try:
        # Lấy danh sách meal plans
        if user_id:
            meal_plans = firestore_service.get_user_meal_plans(user_id)
        else:
            meal_plans = firestore_service.get_all_meal_plans()
        
        # Phân trang
        total_plans = len(meal_plans)
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        plans_page = meal_plans[start_idx:end_idx]
        
        # Tính toán thông tin phân trang
        total_pages = (total_plans + limit - 1) // limit
        has_prev = page > 1
        has_next = page < total_pages
        
        return templates.TemplateResponse("admin/meal_plans.html", {
            "request": request,
            "meal_plans": plans_page,
            "current_page": page,
            "total_pages": total_pages,
            "total_plans": total_plans,
            "has_prev": has_prev,
            "has_next": has_next,
            "user_id": user_id or ""
        })
    except Exception as e:
        print(f"Error in admin meal plans: {str(e)}")
        return templates.TemplateResponse("admin/meal_plans.html", {
            "request": request,
            "meal_plans": [],
            "current_page": 1,
            "total_pages": 1,
            "total_plans": 0,
            "has_prev": False,
            "has_next": False,
            "user_id": user_id or "",
            "error": f"Lỗi khi tải dữ liệu kế hoạch bữa ăn: {str(e)}"
        })

@router.get("/reports", response_class=HTMLResponse)
async def admin_reports(
    request: Request,
    start_date: Optional[str] = Query(None),
    end_date: Optional[str] = Query(None),
    templates: Jinja2Templates = Depends(get_templates)
):
    """Trang báo cáo và thống kê"""
    try:
        # Thiết lập ngày mặc định nếu không có
        if not start_date:
            start_date = (datetime.now() - timedelta(days=30)).strftime("%Y-%m-%d")
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")

        # Lấy dữ liệu metrics
        metrics = get_report_metrics(start_date, end_date)

        # Lấy dữ liệu biểu đồ
        chart_data = get_report_chart_data(start_date, end_date)

        # Lấy top users
        top_users = get_top_active_users()

        # Lấy lỗi gần đây
        recent_errors = get_recent_errors()

        return templates.TemplateResponse("admin/reports.html", {
            "request": request,
            "start_date": start_date,
            "end_date": end_date,
            "metrics": metrics,
            "activity_labels": chart_data["activity_labels"],
            "activity_data": chart_data["activity_data"],
            "api_calls_data": chart_data["api_calls_data"],
            "device_labels": chart_data["device_labels"],
            "device_data": chart_data["device_data"],
            "popular_foods_labels": chart_data["popular_foods_labels"],
            "popular_foods_data": chart_data["popular_foods_data"],
            "feature_labels": chart_data["feature_labels"],
            "feature_data": chart_data["feature_data"],
            "top_users": top_users,
            "recent_errors": recent_errors
        })
    except Exception as e:
        print(f"Error in admin reports: {str(e)}")
        # Trả về trang với dữ liệu mặc định
        return templates.TemplateResponse("admin/reports.html", {
            "request": request,
            "start_date": start_date or datetime.now().strftime("%Y-%m-%d"),
            "end_date": end_date or datetime.now().strftime("%Y-%m-%d"),
            "metrics": {},
            "activity_labels": [],
            "activity_data": [],
            "api_calls_data": [],
            "device_labels": [],
            "device_data": [],
            "popular_foods_labels": [],
            "popular_foods_data": [],
            "feature_labels": [],
            "feature_data": [],
            "top_users": [],
            "recent_errors": [],
            "error": f"Lỗi khi tải báo cáo: {str(e)}"
        })

def get_report_metrics(start_date: str, end_date: str):
    """Lấy các metrics cho báo cáo"""
    try:
        # Giả lập dữ liệu metrics
        return {
            "total_api_calls": 15420,
            "api_calls_growth": 12.5,
            "new_users": 89,
            "new_users_growth": 8.3,
            "meal_plans_created": 234,
            "meal_plans_growth": 15.7,
            "activity_rate": 78.5,
            "activity_rate_change": 3.2
        }
    except Exception as e:
        print(f"Error getting report metrics: {str(e)}")
        return {}

def get_report_chart_data(start_date: str, end_date: str):
    """Lấy dữ liệu cho các biểu đồ"""
    try:
        # Tạo dữ liệu giả lập cho biểu đồ
        days = []
        activity_data = []
        api_calls_data = []

        # Tạo dữ liệu cho 30 ngày
        for i in range(30):
            date = datetime.now() - timedelta(days=29-i)
            days.append(date.strftime("%d/%m"))
            activity_data.append(50 + i * 2 + (i % 7) * 10)
            api_calls_data.append(200 + i * 5 + (i % 5) * 20)

        return {
            "activity_labels": days,
            "activity_data": activity_data,
            "api_calls_data": api_calls_data,
            "device_labels": ["Mobile", "Desktop", "Tablet"],
            "device_data": [65, 25, 10],
            "popular_foods_labels": ["Phở bò", "Cơm gà", "Bún bò Huế", "Bánh mì", "Chả cá"],
            "popular_foods_data": [45, 38, 32, 28, 25],
            "feature_labels": ["Tạo meal plan", "Nhận diện thực phẩm", "Chat AI", "Theo dõi nước", "Bài tập"],
            "feature_data": [35, 25, 20, 12, 8]
        }
    except Exception as e:
        print(f"Error getting chart data: {str(e)}")
        return {
            "activity_labels": [],
            "activity_data": [],
            "api_calls_data": [],
            "device_labels": [],
            "device_data": [],
            "popular_foods_labels": [],
            "popular_foods_data": [],
            "feature_labels": [],
            "feature_data": []
        }

def get_top_active_users():
    """Lấy danh sách người dùng hoạt động nhất"""
    try:
        # Giả lập dữ liệu
        return [
            {
                "display_name": "Nguyễn Văn A",
                "email": "<EMAIL>",
                "photo_url": None,
                "activity_count": 45,
                "meal_plans_count": 8,
                "last_activity": "2 giờ trước"
            },
            {
                "display_name": "Trần Thị B",
                "email": "<EMAIL>",
                "photo_url": None,
                "activity_count": 38,
                "meal_plans_count": 6,
                "last_activity": "5 giờ trước"
            },
            {
                "display_name": "Lê Văn C",
                "email": "<EMAIL>",
                "photo_url": None,
                "activity_count": 32,
                "meal_plans_count": 5,
                "last_activity": "1 ngày trước"
            }
        ]
    except Exception as e:
        print(f"Error getting top users: {str(e)}")
        return []

def get_recent_errors():
    """Lấy danh sách lỗi gần đây"""
    try:
        # Giả lập dữ liệu lỗi
        return [
            {
                "type": "API Timeout",
                "message": "Groq API timeout khi tạo meal plan",
                "level": "warning",
                "count": 3
            },
            {
                "type": "Database Connection",
                "message": "Kết nối Firestore bị gián đoạn",
                "level": "error",
                "count": 1
            }
        ]
    except Exception as e:
        print(f"Error getting recent errors: {str(e)}")
        return []

@router.get("/settings", response_class=HTMLResponse)
async def admin_settings(
    request: Request,
    templates: Jinja2Templates = Depends(get_templates)
):
    """Trang cấu hình hệ thống"""
    try:
        # Lấy trạng thái hệ thống
        system_status = get_system_status()

        # Lấy cấu hình hiện tại
        settings = get_current_settings()

        return templates.TemplateResponse("admin/settings.html", {
            "request": request,
            "system_status": system_status,
            "settings": settings
        })
    except Exception as e:
        print(f"Error in admin settings: {str(e)}")
        return templates.TemplateResponse("admin/settings.html", {
            "request": request,
            "system_status": {
                "database_connected": False,
                "ai_service_available": False,
                "firebase_connected": False,
                "storage_available": False
            },
            "settings": {},
            "error": f"Lỗi khi tải cấu hình: {str(e)}"
        })

def get_current_settings():
    """Lấy cấu hình hiện tại của hệ thống"""
    try:
        import os

        # Lấy cấu hình từ biến môi trường và cấu hình mặc định
        settings = {
            # AI & API Settings
            "groq_api_key": "***" if os.getenv("GROQ_API_KEY") else "",
            "groq_model": "llama3-8b-8192",
            "groq_timeout": 30,
            "usda_api_key": "***" if os.getenv("USDA_API_KEY") else "",
            "usda_max_results": 10,
            "usda_cache_enabled": True,
            "rate_limit_per_minute": 60,
            "rate_limit_per_day": 1000,
            "cache_expiry_hours": 24,
            "cache_enabled": True,

            # Database Settings
            "firebase_project_id": os.getenv("FIREBASE_PROJECT_ID", ""),
            "firebase_storage_bucket": os.getenv("FIREBASE_STORAGE_BUCKET", ""),
            "firebase_emulator_enabled": False,
            "connection_pool_size": 10,
            "query_timeout": 30,
            "enable_query_logging": False,

            # Security Settings
            "jwt_secret": "***" if os.getenv("JWT_SECRET") else "",
            "token_expiry_hours": 24,
            "require_email_verification": False,
            "allowed_origins": "*",
            "enable_cors": True,
            "force_https": False,

            # Performance Settings
            "max_workers": 4,
            "request_timeout": 30,
            "enable_gzip": True,
            "log_level": "INFO",
            "enable_metrics": True,
            "enable_health_check": True,

            # Notification Settings
            "smtp_host": "",
            "smtp_port": 587,
            "smtp_username": "",
            "smtp_password": "",
            "admin_email": "",
            "alert_on_errors": True,
            "alert_on_high_usage": True,
            "daily_reports": False
        }

        return settings
    except Exception as e:
        print(f"Error getting current settings: {str(e)}")
        return {}

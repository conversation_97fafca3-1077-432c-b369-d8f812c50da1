/* Tùy chỉnh màu sắc và font */
:root {
  --primary-color: #28a745;
  --secondary-color: #6c757d;
  --accent-color: #ffc107;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
}

.navbar {
  box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.card {
  border-radius: 10px;
  box-shadow: 0 4px 6px rgba(0,0,0,.05);
  transition: transform 0.3s;
}

.card:hover {
  transform: translateY(-5px);
}

.footer {
  border-top: 1px solid #dee2e6;
}

/* Tùy chỉnh cho trang danh sách món ăn */
.nutrition-info {
  border-top: 1px solid #dee2e6;
  padding-top: 10px;
  margin-top: 10px;
}

/* Style cho form */
.form-container {
  background-color: #fff;
  border-radius: 10px;
  padding: 20px;
  box-shadow: 0 0 15px rgba(0,0,0,.05);
}

/* Style cho nút */
.btn-primary {
  background-color: var(--primary-color);
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Style cho trang chi tiết món ăn */
.food-detail-card {
  background-color: #fff;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 5px 15px rgba(0,0,0,.1);
}

.food-header {
  background-color: var(--primary-color);
  color: white;
  padding: 20px;
}

.ingredient-list {
  list-style-type: none;
  padding-left: 0;
}

.ingredient-list li {
  padding: 8px 0;
  border-bottom: 1px solid #eee;
}

.preparation-step {
  margin-bottom: 15px;
  padding: 10px;
  background-color: #f8f9fa;
  border-left: 3px solid var(--accent-color);
  border-radius: 5px;
} 
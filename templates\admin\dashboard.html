{% extends "base.html" %}

{% block title %}Admin Dashboard - OpenFood Management{% endblock %}

{% block page_title %}Dashboard Tổng Quan{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="refreshStats()">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
    <button type="button" class="btn btn-outline-success" onclick="exportReport()">
        <i class="fas fa-download"></i> Xuất báo cáo
    </button>
</div>
{% endblock %}

{% block content %}
<!-- Statistics Cards -->
<div class="row mb-4">
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Tổng số món ăn</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_foods or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-hamburger fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-success">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Người dùng hoạt động</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.active_users or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-users fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-warning">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">Kế hoạch bữa ăn</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.total_meal_plans or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-calendar-alt fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card stat-card-info">
            <div class="card-body">
                <div class="row no-gutters align-items-center">
                    <div class="col mr-2">
                        <div class="text-xs font-weight-bold text-uppercase mb-1">API Calls hôm nay</div>
                        <div class="h5 mb-0 font-weight-bold">{{ stats.api_calls_today or 0 }}</div>
                    </div>
                    <div class="col-auto">
                        <i class="fas fa-chart-line fa-2x opacity-75"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Charts Row -->
<div class="row mb-4">
    <div class="col-xl-8 col-lg-7">
        <div class="card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Hoạt động hệ thống (7 ngày qua)</h6>
            </div>
            <div class="card-body">
                <div class="chart-area">
                    <canvas id="activityChart"></canvas>
                </div>
            </div>
        </div>
    </div>

    <div class="col-xl-4 col-lg-5">
        <div class="card">
            <div class="card-header py-3 d-flex flex-row align-items-center justify-content-between">
                <h6 class="m-0 font-weight-bold text-primary">Loại món ăn phổ biến</h6>
            </div>
            <div class="card-body">
                <div class="chart-pie pt-4 pb-2">
                    <canvas id="foodTypeChart"></canvas>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Activity -->
<div class="row">
    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Hoạt động gần đây</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for activity in recent_activities %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">{{ activity.action }}</div>
                            <small class="text-muted">{{ activity.description }}</small>
                        </div>
                        <small class="text-muted">{{ activity.timestamp }}</small>
                    </div>
                    {% else %}
                    <div class="list-group-item text-center text-muted">
                        Chưa có hoạt động nào
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>

    <div class="col-lg-6 mb-4">
        <div class="card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Món ăn được tạo gần đây</h6>
            </div>
            <div class="card-body">
                <div class="list-group list-group-flush">
                    {% for food in recent_foods %}
                    <div class="list-group-item d-flex justify-content-between align-items-center">
                        <div>
                            <div class="fw-bold">{{ food.name }}</div>
                            <small class="text-muted">{{ food.nutrition.calories }} kcal</small>
                        </div>
                        <div class="text-end">
                            <small class="text-muted">{{ food.created_at }}</small>
                            <br>
                            <a href="/admin/foods" class="btn btn-sm btn-outline-primary">Xem tất cả</a>
                        </div>
                    </div>
                    {% else %}
                    <div class="list-group-item text-center text-muted">
                        Chưa có món ăn nào
                    </div>
                    {% endfor %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header py-3">
                <h6 class="m-0 font-weight-bold text-primary">Trạng thái hệ thống</h6>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator bg-success me-2"></div>
                            <div>
                                <div class="fw-bold">Database</div>
                                <small class="text-muted">Hoạt động bình thường</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator {% if system_status.ai_available %}bg-success{% else %}bg-warning{% endif %} me-2"></div>
                            <div>
                                <div class="fw-bold">AI Service</div>
                                <small class="text-muted">
                                    {% if system_status.ai_available %}
                                        {{ system_status.ai_type }}
                                    {% else %}
                                        Không khả dụng
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator {% if system_status.firebase_connected %}bg-success{% else %}bg-danger{% endif %} me-2"></div>
                            <div>
                                <div class="fw-bold">Firebase</div>
                                <small class="text-muted">
                                    {% if system_status.firebase_connected %}
                                        Kết nối thành công
                                    {% else %}
                                        Lỗi kết nối
                                    {% endif %}
                                </small>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="d-flex align-items-center">
                            <div class="status-indicator bg-success me-2"></div>
                            <div>
                                <div class="fw-bold">API Server</div>
                                <small class="text-muted">Hoạt động bình thường</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .status-indicator {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        display: inline-block;
    }
    .chart-area {
        position: relative;
        height: 300px;
    }
    .chart-pie {
        position: relative;
        height: 250px;
    }
</style>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
// Activity Chart
const activityCtx = document.getElementById('activityChart').getContext('2d');
const activityChart = new Chart(activityCtx, {
    type: 'line',
    data: {
        labels: {{ activity_chart_labels | tojson }},
        datasets: [{
            label: 'API Calls',
            data: {{ activity_chart_data | tojson }},
            borderColor: 'rgb(75, 192, 192)',
            backgroundColor: 'rgba(75, 192, 192, 0.2)',
            tension: 0.1
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false,
        scales: {
            y: {
                beginAtZero: true
            }
        }
    }
});

// Food Type Chart
const foodTypeCtx = document.getElementById('foodTypeChart').getContext('2d');
const foodTypeChart = new Chart(foodTypeCtx, {
    type: 'doughnut',
    data: {
        labels: {{ food_type_labels | tojson }},
        datasets: [{
            data: {{ food_type_data | tojson }},
            backgroundColor: [
                '#FF6384',
                '#36A2EB',
                '#FFCE56',
                '#4BC0C0',
                '#9966FF',
                '#FF9F40'
            ]
        }]
    },
    options: {
        responsive: true,
        maintainAspectRatio: false
    }
});

function refreshStats() {
    location.reload();
}

function exportReport() {
    // Implement export functionality
    alert('Tính năng xuất báo cáo sẽ được triển khai sau');
}
</script>
{% endblock %}

{% extends 'base.html' %}

{% block title %}{{ food.name }} - Chi tiết món ăn{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-8">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item"><a href="/">Trang chủ</a></li>
                <li class="breadcrumb-item"><a href="/food">Món ăn</a></li>
                <li class="breadcrumb-item active" aria-current="page">{{ food.name }}</li>
            </ol>
        </nav>
    </div>
    <div class="col-md-4 text-end">
        <a href="/food/edit/{{ food.id }}" class="btn btn-warning">
            <span class="me-1">✏️</span> Sửa món ăn
        </a>
        <button class="btn btn-danger ms-2" onclick="confirmDelete('{{ food.id }}')">
            <span class="me-1">🗑️</span> Xóa món ăn
        </button>
    </div>
</div>

<div class="food-detail-card mb-5">
    <div class="food-header">
        <h1>{{ food.name }}</h1>
        <p>{{ food.description }}</p>
        <div class="mt-3">
            <span class="badge bg-light text-dark me-2">Thời gian: {{ food.preparation_time }}</span>
            {% if food.meal_type %}
                <span class="badge bg-info me-2">{{ food.meal_type }}</span>
            {% endif %}
            {% if food.cuisine_style %}
                <span class="badge bg-secondary">{{ food.cuisine_style }}</span>
            {% endif %}
        </div>
    </div>

    <div class="container py-4">
        <div class="row">
            <div class="col-md-4">
                <div class="card mb-4">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">Giá trị dinh dưỡng</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-6 text-center">
                                <div class="nutrition-circle">
                                    <h2>{{ food.nutrition.calories }}</h2>
                                    <small>kcal</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <ul class="list-unstyled">
                                    <li><strong>Protein:</strong> {{ food.nutrition.protein }}g</li>
                                    <li><strong>Chất béo:</strong> {{ food.nutrition.fat }}g</li>
                                    <li><strong>Carbs:</strong> {{ food.nutrition.carbs }}g</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer">
                        <small class="text-muted">Thông tin dinh dưỡng cho 1 phần ăn</small>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header bg-info text-white">
                        <h5 class="mb-0">Lợi ích sức khỏe</h5>
                    </div>
                    <div class="card-body">
                        <p>{{ food.health_benefits }}</p>
                    </div>
                </div>
            </div>

            <div class="col-md-8">
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Nguyên liệu</h5>
                    </div>
                    <div class="card-body">
                        <ul class="ingredient-list">
                            {% for ingredient in food.ingredients %}
                                <li><strong>{{ ingredient.name }}:</strong> {{ ingredient.amount }}</li>
                            {% endfor %}
                        </ul>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Cách chế biến</h5>
                    </div>
                    <div class="card-body">
                        <ol class="preparation-list">
                            {% for step in food.preparation %}
                                <li class="preparation-step">{{ step }}</li>
                            {% endfor %}
                        </ol>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .nutrition-circle {
        border-radius: 50%;
        width: 80px;
        height: 80px;
        background-color: #f8f9fa;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        margin: 0 auto;
        border: 3px solid #28a745;
    }
    
    .nutrition-circle h2 {
        margin: 0;
        font-size: 24px;
    }
    
    .preparation-list {
        padding-left: 1.5rem;
    }
</style>
{% endblock %}

{% extends "base.html" %}

{% block title %}Quản lý người dùng - OpenFood Admin{% endblock %}

{% block page_title %}Quản lý người dùng{% endblock %}

{% block page_actions %}
<div class="btn-group" role="group">
    <button type="button" class="btn btn-outline-primary" onclick="refreshUsers()">
        <i class="fas fa-sync-alt"></i> Làm mới
    </button>
    <button type="button" class="btn btn-outline-success" onclick="exportUsers()">
        <i class="fas fa-download"></i> Xuất danh sách
    </button>
</div>
{% endblock %}

{% block content %}
<!-- Search and Filter -->
<div class="row mb-4">
    <div class="col-md-8">
        <form method="GET" class="d-flex">
            <input type="text" class="form-control me-2" name="search" placeholder="T<PERSON><PERSON> kiếm theo email hoặc tên..." value="{{ search }}">
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-search"></i> Tìm kiếm
            </button>
        </form>
    </div>
    <div class="col-md-4 text-end">
        <div class="text-muted">
            Tổng cộng: <strong>{{ total_users }}</strong> người dùng
        </div>
    </div>
</div>

{% if error %}
<div class="alert alert-danger" role="alert">
    <i class="fas fa-exclamation-triangle"></i> {{ error }}
</div>
{% endif %}

<!-- Users Table -->
<div class="card">
    <div class="card-header">
        <h6 class="m-0 font-weight-bold text-primary">Danh sách người dùng</h6>
    </div>
    <div class="card-body">
        {% if users %}
        <div class="table-responsive">
            <table class="table table-hover">
                <thead class="table-light">
                    <tr>
                        <th>Avatar</th>
                        <th>Thông tin</th>
                        <th>Trạng thái</th>
                        <th>Hoạt động</th>
                        <th>Ngày tạo</th>
                        <th>Thao tác</th>
                    </tr>
                </thead>
                <tbody>
                    {% for user in users %}
                    <tr>
                        <td>
                            {% if user.photo_url %}
                            <img src="{{ user.photo_url }}" alt="Avatar" class="rounded-circle" width="40" height="40">
                            {% else %}
                            <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center" style="width: 40px; height: 40px;">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            {% endif %}
                        </td>
                        <td>
                            <div>
                                <strong>{{ user.display_name or 'Chưa có tên' }}</strong>
                                {% if user.email_verified %}
                                <i class="fas fa-check-circle text-success ms-1" title="Email đã xác thực"></i>
                                {% endif %}
                            </div>
                            <small class="text-muted">{{ user.email }}</small>
                            {% if user.is_anonymous %}
                            <br><span class="badge bg-warning">Tài khoản ẩn danh</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.disabled %}
                            <span class="badge bg-danger">Bị khóa</span>
                            {% else %}
                            <span class="badge bg-success">Hoạt động</span>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.last_login %}
                            <small class="text-muted">{{ user.last_login }}</small>
                            {% else %}
                            <small class="text-muted">Chưa đăng nhập</small>
                            {% endif %}
                        </td>
                        <td>
                            {% if user.created_at %}
                            <small class="text-muted">{{ user.created_at }}</small>
                            {% else %}
                            <small class="text-muted">Không rõ</small>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <button type="button" class="btn btn-outline-primary" onclick="viewUser('{{ user.uid }}')">
                                    <i class="fas fa-eye"></i>
                                </button>
                                <button type="button" class="btn btn-outline-info" onclick="viewUserMealPlans('{{ user.uid }}')">
                                    <i class="fas fa-calendar-alt"></i>
                                </button>
                                {% if not user.disabled %}
                                <button type="button" class="btn btn-outline-warning" onclick="disableUser('{{ user.uid }}')">
                                    <i class="fas fa-ban"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-success" onclick="enableUser('{{ user.uid }}')">
                                    <i class="fas fa-check"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>

        <!-- Pagination -->
        {% if total_pages > 1 %}
        <nav aria-label="User pagination">
            <ul class="pagination justify-content-center">
                {% if has_prev %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page - 1 }}{% if search %}&search={{ search }}{% endif %}">Trước</a>
                </li>
                {% endif %}
                
                {% for page_num in range(1, total_pages + 1) %}
                {% if page_num == current_page %}
                <li class="page-item active">
                    <span class="page-link">{{ page_num }}</span>
                </li>
                {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_num }}{% if search %}&search={{ search }}{% endif %}">{{ page_num }}</a>
                </li>
                {% elif page_num == 4 or page_num == total_pages - 3 %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
                {% endfor %}
                
                {% if has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ current_page + 1 }}{% if search %}&search={{ search }}{% endif %}">Sau</a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
        {% else %}
        <div class="text-center py-5">
            <i class="fas fa-users fa-3x text-muted mb-3"></i>
            <h5 class="text-muted">Không có người dùng nào</h5>
            <p class="text-muted">
                {% if search %}
                Không tìm thấy người dùng nào với từ khóa "{{ search }}"
                {% else %}
                Hệ thống chưa có người dùng nào đăng ký
                {% endif %}
            </p>
        </div>
        {% endif %}
    </div>
</div>

<!-- User Detail Modal -->
<div class="modal fade" id="userDetailModal" tabindex="-1" aria-labelledby="userDetailModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userDetailModalLabel">Chi tiết người dùng</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="userDetailContent">
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Đang tải...</span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function refreshUsers() {
    location.reload();
}

function exportUsers() {
    // Implement export functionality
    alert('Tính năng xuất danh sách người dùng sẽ được triển khai sau');
}

function viewUser(userId) {
    // Show user detail modal
    const modal = new bootstrap.Modal(document.getElementById('userDetailModal'));
    const content = document.getElementById('userDetailContent');
    
    // Show loading
    content.innerHTML = `
        <div class="text-center">
            <div class="spinner-border" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
        </div>
    `;
    
    modal.show();
    
    // Load user details (implement API call)
    setTimeout(() => {
        content.innerHTML = `
            <div class="row">
                <div class="col-md-4 text-center">
                    <div class="bg-secondary rounded-circle d-flex align-items-center justify-content-center mx-auto mb-3" style="width: 80px; height: 80px;">
                        <i class="fas fa-user text-white fa-2x"></i>
                    </div>
                </div>
                <div class="col-md-8">
                    <h6>Thông tin cơ bản</h6>
                    <p><strong>ID:</strong> ${userId}</p>
                    <p><strong>Email:</strong> <EMAIL></p>
                    <p><strong>Tên hiển thị:</strong> Người dùng mẫu</p>
                    <p><strong>Trạng thái:</strong> <span class="badge bg-success">Hoạt động</span></p>
                    <hr>
                    <h6>Thống kê hoạt động</h6>
                    <p><strong>Số meal plans:</strong> 5</p>
                    <p><strong>Lần đăng nhập cuối:</strong> 2 giờ trước</p>
                </div>
            </div>
        `;
    }, 1000);
}

function viewUserMealPlans(userId) {
    window.location.href = `/admin/meal-plans?user_id=${userId}`;
}

function disableUser(userId) {
    if (confirm('Bạn có chắc chắn muốn khóa tài khoản này?')) {
        // Implement disable user API call
        alert('Tính năng khóa tài khoản sẽ được triển khai sau');
    }
}

function enableUser(userId) {
    if (confirm('Bạn có chắc chắn muốn mở khóa tài khoản này?')) {
        // Implement enable user API call
        alert('Tính năng mở khóa tài khoản sẽ được triển khai sau');
    }
}
</script>
{% endblock %}

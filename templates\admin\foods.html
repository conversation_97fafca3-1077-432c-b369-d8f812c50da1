{% extends "base.html" %}

{% block title %}Quản lý Món ăn - Admin{% endblock %}

{% block page_title %}Quản lý Món ăn{% endblock %}

{% block page_actions %}
<button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addFoodModal">
    <i class="fas fa-plus mr-1"></i>
    Thêm món ăn
</button>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h3 class="card-title">
                        <i class="fas fa-utensils mr-2"></i>
                        Quản lý Món ăn
                    </h3>
                    <div class="card-tools">
                        <button type="button" class="btn btn-primary" data-toggle="modal" data-target="#addFoodModal">
                            <i class="fas fa-plus mr-1"></i>
                            Thêm món ăn
                        </button>
                    </div>
                </div>
                
                <div class="card-body">
                    <!-- Search Form -->
                    <form method="GET" class="mb-3">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="input-group">
                                    <input type="text" class="form-control" name="search" 
                                           placeholder="Tìm kiếm món ăn..." value="{{ search }}">
                                    <div class="input-group-append">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="text-right">
                                    <span class="text-muted">
                                        Tổng cộng: <strong>{{ total_foods }}</strong> món ăn
                                    </span>
                                </div>
                            </div>
                        </div>
                    </form>

                    {% if error %}
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle mr-2"></i>
                        {{ error }}
                    </div>
                    {% endif %}

                    <!-- Foods Table -->
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Tên món ăn</th>
                                    <th>Mô tả</th>
                                    <th>Calories</th>
                                    <th>Protein (g)</th>
                                    <th>Fat (g)</th>
                                    <th>Carbs (g)</th>
                                    <th>Ngày tạo</th>
                                    <th>Thao tác</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% if foods %}
                                    {% for food in foods %}
                                    <tr>
                                        <td>{{ food.get('id', 'N/A')[:8] }}...</td>
                                        <td>
                                            <strong>{{ food.get('name', 'Không rõ') }}</strong>
                                        </td>
                                        <td>
                                            <span class="text-muted">
                                                {{ food.get('description', 'Không có mô tả')[:50] }}
                                                {% if food.get('description', '')|length > 50 %}...{% endif %}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="badge badge-info">
                                                {{ food.get('nutrition', {}).get('calories', 0) }}
                                            </span>
                                        </td>
                                        <td>{{ food.get('nutrition', {}).get('protein', 0) }}</td>
                                        <td>{{ food.get('nutrition', {}).get('fat', 0) }}</td>
                                        <td>{{ food.get('nutrition', {}).get('carbs', 0) }}</td>
                                        <td>
                                            <small class="text-muted">
                                                {{ food.get('created_at', 'Không rõ') }}
                                            </small>
                                        </td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <button type="button" class="btn btn-sm btn-info" 
                                                        onclick="viewFood('{{ food.get('id', '') }}')"
                                                        title="Xem chi tiết">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="editFood('{{ food.get('id', '') }}')"
                                                        title="Chỉnh sửa">
                                                    <i class="fas fa-edit"></i>
                                                </button>
                                                <button type="button" class="btn btn-sm btn-danger" 
                                                        onclick="deleteFood('{{ food.get('id', '') }}', '{{ food.get('name', '') }}')"
                                                        title="Xóa">
                                                    <i class="fas fa-trash"></i>
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                {% else %}
                                    <tr>
                                        <td colspan="9" class="text-center text-muted">
                                            <i class="fas fa-utensils fa-3x mb-3"></i>
                                            <br>
                                            {% if search %}
                                                Không tìm thấy món ăn nào với từ khóa "{{ search }}"
                                            {% else %}
                                                Chưa có món ăn nào
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endif %}
                            </tbody>
                        </table>
                    </div>

                    <!-- Pagination -->
                    {% if total_pages > 1 %}
                    <nav aria-label="Foods pagination">
                        <ul class="pagination justify-content-center">
                            {% if has_prev %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ current_page - 1 }}{% if search %}&search={{ search }}{% endif %}">
                                        <i class="fas fa-chevron-left"></i>
                                    </a>
                                </li>
                            {% endif %}
                            
                            {% for page_num in range(1, total_pages + 1) %}
                                {% if page_num == current_page %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ page_num }}</span>
                                    </li>
                                {% elif page_num <= 3 or page_num > total_pages - 3 or (page_num >= current_page - 1 and page_num <= current_page + 1) %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ page_num }}{% if search %}&search={{ search }}{% endif %}">
                                            {{ page_num }}
                                        </a>
                                    </li>
                                {% elif page_num == 4 and current_page > 6 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% elif page_num == total_pages - 3 and current_page < total_pages - 5 %}
                                    <li class="page-item disabled">
                                        <span class="page-link">...</span>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ current_page + 1 }}{% if search %}&search={{ search }}{% endif %}">
                                        <i class="fas fa-chevron-right"></i>
                                    </a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Food Modal -->
<div class="modal fade" id="addFoodModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-plus mr-2"></i>
                    Thêm món ăn mới
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="addFoodForm">
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="foodName">Tên món ăn *</label>
                                <input type="text" class="form-control" id="foodName" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label for="foodCategory">Danh mục</label>
                                <select class="form-control" id="foodCategory" name="category">
                                    <option value="">Chọn danh mục</option>
                                    <option value="breakfast">Bữa sáng</option>
                                    <option value="lunch">Bữa trưa</option>
                                    <option value="dinner">Bữa tối</option>
                                    <option value="snack">Đồ ăn vặt</option>
                                    <option value="drink">Đồ uống</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label for="foodDescription">Mô tả</label>
                        <textarea class="form-control" id="foodDescription" name="description" rows="3"></textarea>
                    </div>
                    
                    <h6 class="mt-4 mb-3">
                        <i class="fas fa-chart-bar mr-2"></i>
                        Thông tin dinh dưỡng
                    </h6>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="foodCalories">Calories *</label>
                                <input type="number" class="form-control" id="foodCalories" name="calories" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="foodProtein">Protein (g) *</label>
                                <input type="number" class="form-control" id="foodProtein" name="protein" min="0" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="foodFat">Fat (g) *</label>
                                <input type="number" class="form-control" id="foodFat" name="fat" min="0" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label for="foodCarbs">Carbs (g) *</label>
                                <input type="number" class="form-control" id="foodCarbs" name="carbs" min="0" step="0.1" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save mr-1"></i>
                        Lưu món ăn
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- View Food Modal -->
<div class="modal fade" id="viewFoodModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-eye mr-2"></i>
                    Chi tiết món ăn
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <div class="modal-body" id="viewFoodContent">
                <!-- Content will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Đóng</button>
            </div>
        </div>
    </div>
</div>

<!-- Edit Food Modal -->
<div class="modal fade" id="editFoodModal" tabindex="-1" role="dialog">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-edit mr-2"></i>
                    Chỉnh sửa món ăn
                </h5>
                <button type="button" class="close" data-dismiss="modal">
                    <span>&times;</span>
                </button>
            </div>
            <form id="editFoodForm">
                <div class="modal-body" id="editFoodContent">
                    <!-- Content will be loaded here -->
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Hủy</button>
                    <button type="submit" class="btn btn-warning">
                        <i class="fas fa-save mr-1"></i>
                        Cập nhật
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Food management functions
function viewFood(foodId) {
    // Load food details
    fetch(`/admin/api/foods/${foodId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const food = data.food;
                document.getElementById('viewFoodContent').innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Thông tin cơ bản</h6>
                            <p><strong>Tên:</strong> ${food.name || 'N/A'}</p>
                            <p><strong>Danh mục:</strong> ${food.category || 'N/A'}</p>
                            <p><strong>Mô tả:</strong> ${food.description || 'Không có mô tả'}</p>
                        </div>
                        <div class="col-md-6">
                            <h6>Thông tin dinh dưỡng</h6>
                            <p><strong>Calories:</strong> ${food.nutrition?.calories || 0}</p>
                            <p><strong>Protein:</strong> ${food.nutrition?.protein || 0}g</p>
                            <p><strong>Fat:</strong> ${food.nutrition?.fat || 0}g</p>
                            <p><strong>Carbs:</strong> ${food.nutrition?.carbs || 0}g</p>
                        </div>
                    </div>
                    <hr>
                    <p><strong>Ngày tạo:</strong> ${food.created_at || 'N/A'}</p>
                `;
                $('#viewFoodModal').modal('show');
            } else {
                alert('Không thể tải thông tin món ăn');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tải thông tin món ăn');
        });
}

function editFood(foodId) {
    // Load food for editing
    fetch(`/admin/api/foods/${foodId}`)
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const food = data.food;
                document.getElementById('editFoodContent').innerHTML = `
                    <input type="hidden" name="food_id" value="${food.id}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Tên món ăn *</label>
                                <input type="text" class="form-control" name="name" value="${food.name || ''}" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label>Danh mục</label>
                                <select class="form-control" name="category">
                                    <option value="">Chọn danh mục</option>
                                    <option value="breakfast" ${food.category === 'breakfast' ? 'selected' : ''}>Bữa sáng</option>
                                    <option value="lunch" ${food.category === 'lunch' ? 'selected' : ''}>Bữa trưa</option>
                                    <option value="dinner" ${food.category === 'dinner' ? 'selected' : ''}>Bữa tối</option>
                                    <option value="snack" ${food.category === 'snack' ? 'selected' : ''}>Đồ ăn vặt</option>
                                    <option value="drink" ${food.category === 'drink' ? 'selected' : ''}>Đồ uống</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        <label>Mô tả</label>
                        <textarea class="form-control" name="description" rows="3">${food.description || ''}</textarea>
                    </div>
                    
                    <h6 class="mt-4 mb-3">Thông tin dinh dưỡng</h6>
                    
                    <div class="row">
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Calories *</label>
                                <input type="number" class="form-control" name="calories" value="${food.nutrition?.calories || 0}" min="0" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Protein (g) *</label>
                                <input type="number" class="form-control" name="protein" value="${food.nutrition?.protein || 0}" min="0" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Fat (g) *</label>
                                <input type="number" class="form-control" name="fat" value="${food.nutrition?.fat || 0}" min="0" step="0.1" required>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="form-group">
                                <label>Carbs (g) *</label>
                                <input type="number" class="form-control" name="carbs" value="${food.nutrition?.carbs || 0}" min="0" step="0.1" required>
                            </div>
                        </div>
                    </div>
                `;
                $('#editFoodModal').modal('show');
            } else {
                alert('Không thể tải thông tin món ăn');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi tải thông tin món ăn');
        });
}

function deleteFood(foodId, foodName) {
    if (confirm(`Bạn có chắc chắn muốn xóa món ăn "${foodName}"?`)) {
        fetch(`/admin/api/foods/${foodId}`, {
            method: 'DELETE'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('Xóa món ăn thành công');
                location.reload();
            } else {
                alert('Không thể xóa món ăn: ' + (data.message || 'Lỗi không xác định'));
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('Có lỗi xảy ra khi xóa món ăn');
        });
    }
}

// Form submissions
document.getElementById('addFoodForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const foodData = {
        name: formData.get('name'),
        category: formData.get('category'),
        description: formData.get('description'),
        nutrition: {
            calories: parseFloat(formData.get('calories')),
            protein: parseFloat(formData.get('protein')),
            fat: parseFloat(formData.get('fat')),
            carbs: parseFloat(formData.get('carbs'))
        }
    };
    
    fetch('/admin/api/foods', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(foodData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Thêm món ăn thành công');
            $('#addFoodModal').modal('hide');
            location.reload();
        } else {
            alert('Không thể thêm món ăn: ' + (data.message || 'Lỗi không xác định'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi thêm món ăn');
    });
});

document.getElementById('editFoodForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const foodId = formData.get('food_id');
    const foodData = {
        name: formData.get('name'),
        category: formData.get('category'),
        description: formData.get('description'),
        nutrition: {
            calories: parseFloat(formData.get('calories')),
            protein: parseFloat(formData.get('protein')),
            fat: parseFloat(formData.get('fat')),
            carbs: parseFloat(formData.get('carbs'))
        }
    };
    
    fetch(`/admin/api/foods/${foodId}`, {
        method: 'PUT',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(foodData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert('Cập nhật món ăn thành công');
            $('#editFoodModal').modal('hide');
            location.reload();
        } else {
            alert('Không thể cập nhật món ăn: ' + (data.message || 'Lỗi không xác định'));
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Có lỗi xảy ra khi cập nhật món ăn');
    });
});
</script>
{% endblock %}
